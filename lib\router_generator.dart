import 'package:flutter/material.dart';
import 'package:phoenix/home.dart';
import 'package:phoenix/screens/login/login.dart';
import 'package:phoenix/screens/account/fund_profile_page.dart';
import 'package:phoenix/screens/settings/settings_page.dart';
import 'package:phoenix/screens/splash/splashscreen.dart';
import 'package:phoenix/screens/trades/trades_screen.dart';
import 'package:phoenix/screens/option_chain/option_chain_screen.dart';
import 'package:phoenix/screens/portfolio/portfolio_screen.dart';

class RouterGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: SplashScreen()),
        );

      case '/home':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: Home()),
        );

      case '/login':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: Login()),
        );

      case '/account':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: FundProfilePage()),
        );

      case '/settings':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: SettingsPage()),
        );

      case '/trades':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: TradesScreen()),
        );

      case '/option-chain':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: OptionChainScreen()),
        );

      case '/portfolio':
        return MaterialPageRoute(
          builder: (_) => SafeArea(child: PortfolioScreen()),
        );

      default:
        return MaterialPageRoute(
          builder: (_) => SafeArea(
            child: Scaffold(
              body: Center(child: Text('No route defined for ${settings.name}')),
            ),
          ),
        );
    }
  }
}
