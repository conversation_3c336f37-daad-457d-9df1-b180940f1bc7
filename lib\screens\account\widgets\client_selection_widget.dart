import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/fetch_clients_data/bloc/clients_data_bloc.dart';
import 'package:phoenix/features/fetch_clients_data/model/client_data.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/pnl/bloc/pnl_bloc.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/order_form/selection_drop_down.dart';

///Clients Data selection page
///
class ClientSelectionWidget extends StatefulWidget {
  final int userClientId;

  const ClientSelectionWidget({super.key, required this.userClientId});

  @override
  _ClientSelectionWidgetState createState() => _ClientSelectionWidgetState();
}

class _ClientSelectionWidgetState extends State<ClientSelectionWidget> {
  int? selectedClientId;

  @override
  void initState() {
    super.initState();
    selectedClientId = widget.userClientId;

    context.read<ClientsDataBloc>().add(FetchClientsData());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  BlocBuilder<ClientsDataBloc, ClientsDataState>(
                    builder: (context, state) {
                      if (state is ClientsDataLoading) {
                        return Transform.scale(
                          scale: 0.5,
                          child: CircularLoader(),
                        );
                      } else if (state is ClientsDataLoaded) {
                        final ClientData selectedClient =
                            state.clientsList.isNotEmpty
                                ? state.clientsList.firstWhere(
                                    (element) =>
                                        element.clientId == selectedClientId,
                                    orElse: () => state.clientsList.first,
                                  )
                                : throw Exception("Clients list is empty");

                        return SelectionDropdown<ClientData>(
                          hint: "Account",
                          selectedValue: selectedClient,
                          items: state.clientsList,
                          getItemLabel: (val) => val.clientName,
                          onChanged: (id) {
                            setState(() {
                              selectedClientId = id?.clientId;
                            });
                          },
                          dropdownDecoration: BoxDecoration(
                            color: ThemeConstants.getDropdownBackgroundColor(
                                themeState.isDarkMode),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: ThemeConstants.getDropdownBorderColor(
                                    themeState.isDarkMode)),
                          ),
                          itemTextStyle: TextStyle(
                            color:
                                AppTheme.textSecondary(themeState.isDarkMode),
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                          ),
                        );
                      } else {
                        return GestureDetector(
                          child: Row(
                            children: [
                              Text(
                                "Network error, please try again",
                                style: TextStyle(
                                  color: AppTheme.textPrimary(
                                      themeState.isDarkMode),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Icon(Icons.refresh,
                                  color: AppTheme.primaryColor(
                                      themeState.isDarkMode)),
                            ],
                          ),
                          onTap: () {
                            context
                                .read<ClientsDataBloc>()
                                .add(FetchClientsData());
                          },
                        );
                      }
                    },
                  ),
                  TextButton(
                    onPressed: selectedClientId == widget.userClientId
                        ? null
                        : () {
                            //to refresh the data once the account changes
                            BlocProvider.of<AuthBloc>(context).add(
                              ClientChangeEvent(clientId: selectedClientId!),
                            );
                            BlocProvider.of<PortfolioBloc>(context).add(
                              FetchPortfolio(selectedClientId!),
                            );
                            BlocProvider.of<OrdersStateBloc>(context).add(
                              FetchOrdersState(clientId: selectedClientId!),
                            );
                            BlocProvider.of<PnlBloc>(context).add(
                              FetchPnlData(selectedClientId!),
                            );
                          },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 4,
                      children: [
                        if (selectedClientId == widget.userClientId)
                          Icon(
                            Icons.check,
                            color: Colors.green,
                            size: 15,
                          ),
                        Text(
                          selectedClientId == widget.userClientId
                              ? 'Default'
                              : 'Set as Default',
                          style: TextStyle(
                            color: selectedClientId == widget.userClientId
                                ? Colors.green
                                : AppTheme.primaryColor(themeState.isDarkMode),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              Text(
                'Fund',
                style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 16,
                    fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    '₹ 45,000,00',
                    style: TextStyle(
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                      fontSize: 24,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                  SizedBox(width: 8),
                  Icon(Icons.refresh,
                      color: AppTheme.primaryColor(themeState.isDarkMode)),
                ],
              ),
              SizedBox(height: 8),
              // Row(
              //   children: [
              //     Icon(
              //       Icons.access_time_outlined,
              //       color: Color(0xff989898),
              //       size: 15,
              //     ),
              //     SizedBox(
              //       width: 2,
              //     ),
              //     Text(
              //       'Last updated 2 mins ago',
              //       style: TextStyle(
              //         color: Color(0xff989898),
              //         fontSize: 12,
              //         fontWeight: FontWeight.w600,
              //       ),
              //     ),
              //   ],
              // ),
              //SizedBox(height: 16),
              Text(
                'Beta',
                style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 16,
                    fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 8),
              Text(
                '0.984',
                style: TextStyle(
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                  fontSize: 24,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
